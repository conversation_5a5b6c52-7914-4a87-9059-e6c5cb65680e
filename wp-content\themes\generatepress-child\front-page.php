<?php
/**
 * Homepage Template for Petting Zoo Directory
 */

get_header(); ?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">
        
        <!-- Enhanced Hero Section - Trust & Family Focus -->
        <section class="hero-section">
            <div class="hero-background">
                <img src="<?php echo wp_upload_dir()['baseurl']; ?>/2025/06/placeholder.jpg" alt="Family enjoying petting zoo" class="hero-image">
                <div class="hero-overlay"></div>
            </div>
            <div class="hero-content">
                <div class="container">
                    <h1>Discover the Best Petting Zoos Near You</h1>
                    <p>Find amazing petting zoos, animal farms, and interactive experiences perfect for families. Create lasting memories with hands-on animal encounters that kids and dads love.</p>

                    <!-- Enhanced Zoo Finder Tool with Search -->
                    <form id="zoo-finder-form" class="zoo-finder-form">
                        <div class="search-input-container">
                            <input type="text" id="finder-search" name="search" placeholder="Enter city or state name..." autocomplete="off" aria-label="Search for petting zoos by location">
                            <div id="finder-suggestions" class="search-suggestions"></div>
                        </div>
                        <button type="submit" class="btn btn-primary">Find Petting Zoos</button>
                        <button type="button" id="find-near-me" class="btn btn-secondary">📍 Near Me</button>
                    </form>
                </div>
            </div>
        </section>

        <!-- Explore by State Section -->
        <section class="section" style="background: #ffffff;">
            <div class="container">
                <h2 class="section-title">Explore Petting Zoos by State and City – Discover Family Fun Across America</h2>
                <p class="section-subtitle">Find amazing petting zoos and animal farms in your state or plan your next family road trip. Each state offers unique animal experiences and family-friendly attractions perfect for creating lasting memories.</p>

                <div class="state-cards-grid">
                    <?php
                    // Add console logging for debugging
                    echo '<script>console.log("Homepage: Loading state cards...");</script>';

                    // Get all states with petting zoo counts
                    $all_states = get_terms(array(
                        'taxonomy' => 'location',
                        'hide_empty' => true,
                        'parent' => 0 // Get only parent terms (states)
                    ));

                    echo '<script>console.log("Homepage: Found ' . count($all_states) . ' states");</script>';

                    foreach ($all_states as $state) :
                        // Count petting zoos in this state
                        $state_zoo_count = get_posts(array(
                            'post_type' => 'petting_zoo',
                            'posts_per_page' => -1,
                            'tax_query' => array(
                                array(
                                    'taxonomy' => 'location',
                                    'field'    => 'term_id',
                                    'terms'    => $state->term_id,
                                ),
                            ),
                            'fields' => 'ids'
                        ));
                        $zoo_count = count($state_zoo_count);

                        // Get cities in this state
                        $cities = get_terms(array(
                            'taxonomy' => 'location',
                            'hide_empty' => true,
                            'parent' => $state->term_id
                        ));

                        echo '<script>console.log("Homepage: State ' . esc_js($state->name) . ' has ' . $zoo_count . ' zoos and ' . count($cities) . ' cities");</script>';

                        if ($zoo_count > 0) :
                    ?>
                        <div class="state-card">
                            <div class="state-card-content">
                                <h3 class="state-name">
                                    <a href="/<?php echo strtolower(str_replace(' ', '-', $state->name)); ?>/" class="state-name-link"><?php echo esc_html($state->name); ?></a>
                                </h3>
                                <p class="zoo-count"><?php echo $zoo_count; ?> petting zoo<?php echo $zoo_count !== 1 ? 's' : ''; ?></p>

                                <?php if (!empty($cities)) : ?>
                                    <div class="cities-list">
                                        <?php
                                        foreach ($cities as $city) {
                                            $city_url = '/best-petting-zoos-in-' . strtolower(str_replace(' ', '-', $city->name)) . '-' . strtolower(str_replace(' ', '-', $state->name)) . '/';
                                            echo '<a href="' . esc_url($city_url) . '" class="city-link">' . esc_html($city->name) . '</a>';
                                        }
                                        ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php
                        endif;
                    endforeach;
                    ?>
                </div>
            </div>
        </section>

        <!-- Explore by Animal Section -->
        <section class="section" style="background: #f8f9fa;">
            <div class="container">
                <h2 class="section-title">Petting Zoos by Animal Type – Find Zoos with Goats, Bunnies, Ponies & More</h2>
                <p class="section-subtitle">Looking for a petting zoo with specific animals your kids love? Explore petting zoos across the U.S. based on the animals they feature — from gentle goats and fluffy bunnies to ponies, llamas, reptiles, and more.

Choose an animal below to discover petting zoos that offer safe, hands-on encounters your family won't forget.</p>
                
                <div class="animals-grid">
                    <?php
                    $featured_animals = array(
                                'monkeys' => '🐒', 
                                'gorillas' => '🦍', 
                                'orangutans' => '🦍',
                                'dogs' => '🐶', 
                                'wolves' => '🐺',
                                'foxes' => '🦊', 
                                'raccoons' => '🦝',
                                'cats' => '🐱', 'lions' => '🦁', 'tigers' => '🐯', 'leopards' => '🐆',
                                'horses' => '🐴', 'racehorses' => '🐎', 'miniature horses' => '🐴', 'donkeys' => '🐴', 'ponies' => '🐴',
                                'zebras' => '🦓', 
                                'deers' => '🪼', 
                                'bisons' => '🐃',  'buffalos' => '🐃',
                                'cows' => '🐮', 
                                'oxen' => '🐂', 
                                'pigs' => '🐷', 
                                'boars' => '🐗', 
                                'rams' => '🐏', 
                                'sheep' => '🐑', 
                                'goats' => '🐐',
                                'camels' => '🐫', 
                                'llamas' => '🦙', 'alpacas' => '🦙',
                                'giraffes' => '🦒',
                                'elephants' => '🐘', 
                                'rhinos' => '🦏', 
                                'hippos' => '🦛',
                                'rats' => '🐀', 'mice' => '🐭',
                                'hamsters' => '🐹',
                                'rabbits' => '🐰', 
                                'chipmunks' => '🐿️', 
                                'hedgehogs' => '🦔',
                                'bats' => '🦇', 
                                'bears' => '🐻', 'polar bears' => '🐻‍❄️', 'pandas' => '🐼',
                                'koalas' => '🐨', 
                                'sloths' => '🦥', 
                                'otters' => '🦦',
                                'skunks' => '🦨', 
                                'kangaroos' => '🦘', 
                                'turkeys' => '🦃', 'chickens' => '🐔', 'roosters' => '🐓', 'chicks' => '🐣',
                                'birds' => '🐦', 
                                'penguins' => '🐧', 
                                'doves' => '🕊️',
                                'eagles' => '🦅', 
                                'ducks' => '🦆', 'geese' => '🦢', 'swans' => '🦢',
                                'owls' => '🦉',  
                                'flamingos' => '🦚',
                                'peacocks' => '🦚', 
                                'parrots' => '🦜',
                                'frogs' => '🐸', 
                                'crocodiles' => '🐊', 
                                'turtles' => '🐢', 'tortoises' => '🐢',
                                'reptiles' => '🐍',
                                'lizards' => '🐊',
                                'snakes' => '🐍', 
                                'whales' => '🐳', 
                                'dolphins' => '🐬', 
                                'tropical fish' => '🐠', 'blowfish' => '🐡', 'fish' => '🐟',
                                'sharks' => '🦈', 
                                'octopuses' => '🐙'
                    );
                    
                    // Add console logging for debugging
                    echo '<script>console.log("Homepage: Loading animal terms...");</script>';

                    // Get all animal terms and normalize them
                    $all_animal_terms = get_terms(array(
                        'taxonomy' => 'animal_type',
                        'hide_empty' => true,
                        'number' => 0 // Get all terms first
                    ));

                    echo '<script>console.log("Homepage: Found ' . count($all_animal_terms) . ' animal terms");</script>';

                    // Function to normalize animal names to lowercase plural
                    function normalize_animal_name_to_plural($name) {
                        $name = strtolower(trim($name));

                        // Common singular to plural mappings
                        $singular_to_plural_mappings = array(
                            'cow' => 'cows',
                            'pig' => 'pigs',
                            'goat' => 'goats',
                            'sheep' => 'sheep', // sheep is same for singular/plural
                            'horse' => 'horses',
                            'chicken' => 'chickens',
                            'duck' => 'ducks',
                            'rabbit' => 'rabbits',
                            'bunny' => 'bunnies',
                            'pony' => 'ponies',
                            'llama' => 'llamas',
                            'alpaca' => 'alpacas',
                            'donkey' => 'donkeys',
                            'turkey' => 'turkeys',
                            'goose' => 'geese',
                            'cat' => 'cats',
                            'dog' => 'dogs',
                            'bird' => 'birds',
                            'reptile' => 'reptiles',
                            'snake' => 'snakes',
                            'turtle' => 'turtles',
                            'lizard' => 'lizards',
                            'fish' => 'fish',
                            'mouse' => 'mice',
                            'rat' => 'rats',
                            'hamster' => 'hamsters',
                            'guinea pig' => 'guinea pigs',
                            'ferret' => 'ferrets',
                            'chinchilla' => 'chinchillas',
                            'hedgehog' => 'hedgehogs'
                        );

                        // Check if it's a known singular that should be pluralized
                        if (isset($singular_to_plural_mappings[$name])) {
                            return $singular_to_plural_mappings[$name];
                        }

                        // If it's already plural, keep it
                        if (in_array($name, array_values($singular_to_plural_mappings))) {
                            return $name;
                        }

                        // Simple pluralization for regular words not ending in 's'
                        if (strlen($name) > 2 && substr($name, -1) !== 's' && !in_array($name, array('sheep', 'fish', 'deer'))) {
                            // Handle special cases
                            if (substr($name, -1) === 'y') {
                                return substr($name, 0, -1) . 'ies'; // pony -> ponies
                            } else {
                                return $name . 's'; // goat -> goats
                            }
                        }

                        return $name;
                    }

                    // Group terms by normalized names and sum their counts
                    $normalized_animals = array();
                    foreach ($all_animal_terms as $term) {
                        $normalized_name = normalize_animal_name_to_plural($term->name);
                        $display_name = $normalized_name; // Keep lowercase plural

                        echo '<script>console.log("Homepage: Processing animal term: ' . esc_js($term->name) . ' -> ' . esc_js($normalized_name) . ' (count: ' . $term->count . ')");</script>';

                        if (!isset($normalized_animals[$normalized_name])) {
                            $normalized_animals[$normalized_name] = array(
                                'name' => $display_name,
                                'count' => 0,
                                'slug' => $term->slug // Use first term's slug
                            );
                        }

                        $normalized_animals[$normalized_name]['count'] += $term->count;
                    }

                    echo '<script>console.log("Homepage: Normalized to ' . count($normalized_animals) . ' unique animal types");</script>';

                    // Sort by count and limit to 20
                    uasort($normalized_animals, function($a, $b) {
                        return $b['count'] - $a['count'];
                    });

                    $animal_terms = array_slice($normalized_animals, 0, 20);

                    echo '<script>console.log("Homepage: Displaying top 20 animal types");</script>';

                    foreach ($animal_terms as $normalized_name => $animal) {
                        $icon = isset($featured_animals[$normalized_name]) ? $featured_animals[$normalized_name] : '🐾';
                        ?>
                        <div class="animal-card">
                            <div class="animal-icon"><?php echo $icon; ?></div>
                            <h4><?php echo esc_html($animal['name']); ?></h4>
                            <div class="zoo-count"><?php echo $animal['count']; ?> Location<?php echo $animal['count'] !== 1 ? 's' : ''; ?></div>
                        </div>
                        <?php
                    }
                    ?>
                </div>
            </div>
        </section>

        <!-- Plan Your Visit Section -->
        <section class="section">
            <div class="container">
                <h2 class="section-title">Plan the Ultimate Petting Zoo Experience for Your Family</h2>
                <p class="section-subtitle">Get the most out of your visit by choosing petting zoos that offer exactly what your family needs — from fun animal feeding experiences and birthday party packages to accessible amenities and hands-on learning. Whether you're planning a weekend outing or a special event, our petting zoo features guide helps you find the perfect fit.</p>
                
                <div class="zoo-grid">
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🎯 Choose the Right Petting Zoo for Your Family</h3>
                            <p>Search by zoo type, environment, and activity level. Whether you want a traditional farm-style petting zoo or a hands-on exotic animal park, we'll help you find one that fits your family's comfort and interests.</p>
                        </div>
                    </div>
                    
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🎉 Petting Zoos with Birthday Parties & Special Events</h3>
                            <p>Many petting zoos offer special packages for birthdays, school field trips, and seasonal celebrations. Browse zoos that make party planning simple and fun for kids of all ages.</p>
                        </div>
                    </div>
                    
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🍎 Petting Zoos with Animal Feeding Experiences</h3>
                            <p>Find zoos that let kids feed goats, llamas, rabbits, and more. Some provide feed on-site; others let you bring safe, approved snacks for an interactive animal encounter.</p>
                        </div>
                    </div>
                    
                    <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🚗 Family-Friendly Amenities</h3>
                            <p>Choose locations with picnic tables, shaded areas, clean restrooms, gift shops, and free or on-site parking for a stress-free family outing.</p>
                        </div>
                    </div>
                                        <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🍎 Petting Zoos with Playgrounds or Activity Zones</h3>
                            <p>Extend your visit with zoos that offer kid-friendly playgrounds, obstacle courses, or outdoor games to burn off energy before heading home.</p>
                        </div>
                    </div>
                                        <div class="zoo-card">
                        <div class="zoo-card-content">
                            <h3>🧼 Clean & Safe Petting Zoos</h3>
                            <p>Search for petting zoos with high hygiene ratings, handwashing stations, and staff-trained safety protocols — perfect for toddlers and young children.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- SEO-Enhanced Why Families Love Petting Zoos Section -->
<section class="section" style="background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);">
    <div class="container">
        <h2 class="section-title">Why Families Love Visiting Petting Zoos</h2>
        <p class="section-subtitle">Petting zoos offer a perfect blend of education, adventure, and quality time for families — from toddlers to teens — with interactive animal experiences, outdoor exploration, and bonding opportunities you'll remember for years.</p>

        <div class="zoo-grid">
            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>👨‍👩‍👧‍👦 Quality Family Bonding Time</h3>
                    <p>Enjoy meaningful time together as a family while kids learn and interact with gentle animals. These moments become cherished memories for both parents and children.</p>
                    <div class="features">
                        <span class="feature-tag">No Devices</span>
                        <span class="feature-tag">Shared Smiles</span>
                    </div>
                </div>
            </div>

            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>🎯 Easy Weekend Outings</h3>
                    <p>Petting zoos are low-stress, local adventures that don't require heavy planning. Perfect for spontaneous weekend family fun or organized group visits.</p>
                    <div class="features">
                        <span class="feature-tag">No Reservations Needed</span>
                        <span class="feature-tag">Half-Day Activity</span>
                    </div>
                </div>
            </div>

            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>📵 Educational, Screen-Free Experiences</h3>
                    <p>Step away from screens and dive into the real world of animals. These visits teach responsibility, empathy, and curiosity through hands-on engagement.</p>
                    <div class="features">
                        <span class="feature-tag">STEM Learning</span>
                        <span class="feature-tag">Nature Connection</span>
                    </div>
                </div>
            </div>

            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>💰 Affordable Family Fun</h3>
                    <p>Enjoy budget-friendly outings with flexible pricing and family discounts. Many zoos offer free parking and allow picnics to stretch your dollar further.</p>
                    <div class="features">
                        <span class="feature-tag">Family Discounts</span>
                        <span class="feature-tag">Picnic Friendly</span>
                    </div>
                </div>
            </div>

            <!-- ✅ New Card #1 -->
            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>🧠 Encourages Curiosity & Learning</h3>
                    <p>Petting zoos are more than fun—they're full of teachable moments. Kids ask questions, observe animals up close, and explore new interests in a safe space.</p>
                    <div class="features">
                        <span class="feature-tag">Teachable Moments</span>
                        <span class="feature-tag">Curious Minds</span>
                    </div>
                </div>
            </div>

            <!-- ✅ New Card #2 -->
            <div class="zoo-card">
                <div class="zoo-card-content">
                    <h3>🪑 Accessible for All Family Members</h3>
                    <p>Many petting zoos are stroller- and wheelchair-friendly, with shaded areas, restrooms, and benches — making it easy to include everyone from toddlers to grandparents.</p>
                    <div class="features">
                        <span class="feature-tag">Stroller-Friendly</span>
                        <span class="feature-tag">Accessible Design</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



        <!-- FAQ Section -->
        <section class="section faq-section">
            <div class="container">
                <h2 class="section-title">Frequently Asked Questions</h2>
                
                <div class="faq-item">
                    <div class="faq-question">Can I bring my own food to a petting zoo?</div>
                    <div class="faq-answer">
                        <p>Most petting zoos allow you to bring your own food for picnicking, but policies vary. Some have on-site cafes or snack bars. Always check with the specific petting zoo before your visit.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">What should I wear to a petting zoo?</div>
                    <div class="faq-answer">
                        <p>Wear comfortable, closed-toe shoes and clothes you don't mind getting dirty. Avoid loose jewelry and bring hand sanitizer. Many petting zoos are outdoors, so dress for the weather.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">Are petting zoos safe for young children?</div>
                    <div class="faq-answer">
                        <p>Yes, reputable petting zoos prioritize safety with gentle animals, proper supervision, and safety guidelines. Always supervise young children and follow the zoo's rules for interacting with animals.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">Do I need to make reservations?</div>
                    <div class="faq-answer">
                        <p>While many petting zoos accept walk-ins, it's always best to call ahead, especially for larger groups, special events, or during peak seasons like spring and summer.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">What's the best time to visit a petting zoo?</div>
                    <div class="faq-answer">
                        <p>Animals are typically most active in the morning and late afternoon. Weekdays are usually less crowded than weekends. Spring and fall offer the most comfortable weather for outdoor visits.</p>
                    </div>
                </div>
            </div>
        </section>

    </main>
</div>

<?php get_footer(); ?>
