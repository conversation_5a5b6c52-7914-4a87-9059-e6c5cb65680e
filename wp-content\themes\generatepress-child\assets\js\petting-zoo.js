/**
 * Petting Zoo Directory JavaScript
 */

jQuery(document).ready(function($) {
    
    // Enhanced FAQ Toggle Functionality with accessibility
    $('.faq-question').click(function() {
        var $answer = $(this).next('.faq-answer');
        var $question = $(this);

        // Close all other FAQ answers
        $('.faq-answer').not($answer).removeClass('active').slideUp(300);
        $('.faq-question').not($question).removeClass('active');

        // Toggle current FAQ answer with smooth animation
        $answer.toggleClass('active').slideToggle(300);
        $question.toggleClass('active');

        // Update ARIA attributes for accessibility
        var isExpanded = $answer.hasClass('active');
        $question.attr('aria-expanded', isExpanded);
        $answer.attr('aria-hidden', !isExpanded);
    });
    
    // Enhanced Zoo Finder Functionality with loading states
    $('#zoo-finder-form').on('submit', function(e) {
        e.preventDefault();

        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        var originalText = $submitBtn.text();

        var location = $('#finder-location').val();
        var animalType = $('#finder-animal').val();

        if (!location && !animalType) {
            alert('Please select a location or animal type to search.');
            return;
        }

        // Add loading state with visual feedback
        $submitBtn.text('🔍 Searching...').addClass('loading');
        $form.addClass('loading');

        // Build search URL
        var searchUrl = window.location.origin;

        if (location) {
            // Parse location format: "state:state-slug" or "city:city-slug:state-slug"
            var locationParts = location.split(':');

            if (locationParts[0] === 'state') {
                // State page: /state-name/
                searchUrl += '/' + locationParts[1] + '/';
            } else if (locationParts[0] === 'city') {
                // City page: /best-petting-zoos-in-city-state/
                var citySlug = locationParts[1];
                var stateSlug = locationParts[2];
                searchUrl += '/best-petting-zoos-in-' + citySlug + '-' + stateSlug + '/';
            }
        }

        if (animalType) {
            if (location) {
                searchUrl += '?animal=' + animalType;
            } else {
                searchUrl += '/animals/' + animalType + '/';
            }
        }

        // Simulate brief loading for better UX
        setTimeout(function() {
            window.location.href = searchUrl;
        }, 500);
    });
    
    // Enhanced Geolocation for "Near Me" functionality
    $('#find-near-me').click(function(e) {
        e.preventDefault();

        var $btn = $(this);
        var originalText = $btn.text();

        if (navigator.geolocation) {
            $btn.text('📍 Finding location...').addClass('loading');

            navigator.geolocation.getCurrentPosition(function(position) {
                var lat = position.coords.latitude;
                var lng = position.coords.longitude;

                $btn.text('📍 Redirecting...');

                // Redirect with geolocation data (simplified approach)
                setTimeout(function() {
                    window.location.href = window.location.origin + '/zoos/?near_me=1&lat=' + lat + '&lng=' + lng;
                }, 500);

            }, function(error) {
                var errorMsg = 'Unable to get your location. Please select a city manually.';

                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMsg = 'Location access denied. Please select a city manually.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMsg = 'Location information unavailable. Please select a city manually.';
                        break;
                    case error.TIMEOUT:
                        errorMsg = 'Location request timed out. Please select a city manually.';
                        break;
                }

                alert(errorMsg);
                $btn.text(originalText).removeClass('loading');
            });
        } else {
            alert('Geolocation is not supported by this browser. Please select a city manually.');
        }
    });
    
    // Filter functionality for taxonomy pages
    $('.zoo-filters').on('change', 'select', function() {
        var filters = {};
        
        $('.zoo-filters select').each(function() {
            var filterName = $(this).attr('name');
            var filterValue = $(this).val();
            
            if (filterValue) {
                filters[filterName] = filterValue;
            }
        });
        
        // Build query string
        var queryString = $.param(filters);
        var currentUrl = window.location.pathname;
        
        if (queryString) {
            window.location.href = currentUrl + '?' + queryString;
        } else {
            window.location.href = currentUrl;
        }
    });
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
    
    // Load more functionality for infinite scroll (if needed)
    var page = 2;
    var loading = false;
    
    $('#load-more-zoos').click(function(e) {
        e.preventDefault();
        
        if (loading) return;
        
        loading = true;
        $(this).text('Loading...');
        
        $.ajax({
            url: pettingZooAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'load_more_zoos',
                page: page,
                nonce: pettingZooAjax.nonce,
                // Add any current filters
                filters: getCurrentFilters()
            },
            success: function(response) {
                if (response.success && response.data.html) {
                    $('.zoo-grid').append(response.data.html);
                    page++;
                    
                    if (!response.data.has_more) {
                        $('#load-more-zoos').hide();
                    } else {
                        $('#load-more-zoos').text('Load More Petting Zoos');
                    }
                } else {
                    $('#load-more-zoos').hide();
                }
                loading = false;
            },
            error: function() {
                $('#load-more-zoos').text('Load More Petting Zoos');
                loading = false;
            }
        });
    });
    
    // Helper function to get current filters
    function getCurrentFilters() {
        var filters = {};
        var urlParams = new URLSearchParams(window.location.search);
        
        urlParams.forEach(function(value, key) {
            filters[key] = value;
        });
        
        return filters;
    }
    
    // Initialize tooltips (if using Bootstrap or similar)
    if (typeof $().tooltip === 'function') {
        $('[data-toggle="tooltip"]').tooltip();
    }
    
    // Image lazy loading fallback
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
    
    // Mobile menu toggle (if needed)
    $('.mobile-menu-toggle').click(function() {
        $('.mobile-menu').toggleClass('active');
    });
    
    // Search autocomplete functionality
    $('#zoo-search-input').on('input', function() {
        var query = $(this).val();
        
        if (query.length >= 3) {
            $.ajax({
                url: pettingZooAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'zoo_search_autocomplete',
                    query: query,
                    nonce: pettingZooAjax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        displaySearchSuggestions(response.data);
                    }
                }
            });
        } else {
            hideSearchSuggestions();
        }
    });
    
    function displaySearchSuggestions(suggestions) {
        var suggestionsHtml = '';
        
        suggestions.forEach(function(suggestion) {
            suggestionsHtml += '<div class="search-suggestion" data-url="' + suggestion.url + '">' + suggestion.title + '</div>';
        });
        
        $('#search-suggestions').html(suggestionsHtml).show();
    }
    
    function hideSearchSuggestions() {
        $('#search-suggestions').hide();
    }
    
    // Handle search suggestion clicks
    $(document).on('click', '.search-suggestion', function() {
        window.location.href = $(this).data('url');
    });
    
    // Hide suggestions when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('#zoo-search-input, #search-suggestions').length) {
            hideSearchSuggestions();
        }
    });

    // Enhanced mobile-friendly touch interactions
    if ('ontouchstart' in window) {
        $('.zoo-card, .city-card, .animal-card').on('touchstart', function() {
            $(this).addClass('touch-active');
        }).on('touchend', function() {
            var $this = $(this);
            setTimeout(function() {
                $this.removeClass('touch-active');
            }, 150);
        });
    }

    // Enhanced card hover effects with performance optimization
    $('.zoo-card, .city-card, .animal-card').hover(
        function() {
            $(this).addClass('hover-active');
        },
        function() {
            $(this).removeClass('hover-active');
        }
    );

    // Improved smooth scrolling with easing
    $('a[href^="#"]').off('click').on('click', function(event) {
        var target = $(this.getAttribute('href'));

        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 800, 'swing');
        }
    });

    // Add loading states for all buttons
    $('button, .btn').on('click', function() {
        var $btn = $(this);
        if (!$btn.hasClass('no-loading')) {
            $btn.addClass('loading');
            setTimeout(function() {
                $btn.removeClass('loading');
            }, 2000);
        }
    });

    // Enhanced form validation
    $('form').on('submit', function() {
        var $form = $(this);
        var isValid = true;

        $form.find('input[required], select[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('error');
                isValid = false;
            } else {
                $(this).removeClass('error');
            }
        });

        if (!isValid) {
            alert('Please fill in all required fields.');
            return false;
        }
    });

    // Focus management for accessibility
    $('input, select, textarea').on('focus', function() {
        $(this).closest('.form-group, .zoo-finder').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.form-group, .zoo-finder').removeClass('focused');
    });

});
