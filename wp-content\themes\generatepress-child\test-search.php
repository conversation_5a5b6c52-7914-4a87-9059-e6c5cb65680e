<?php
/**
 * Test Search Functionality
 * Access via: http://pettingzoo.local/wp-content/themes/generatepress-child/test-search.php
 */

// Load WordPress
require_once('../../../wp-load.php');

echo '<h1>Search Functionality Test</h1>';

// Test 1: Check if petting zoos exist
echo '<h2>Test 1: Petting Zoos in Database</h2>';
$all_zoos = get_posts(array(
    'post_type' => 'petting_zoo',
    'posts_per_page' => 10,
    'post_status' => 'publish'
));

echo '<p>Found ' . count($all_zoos) . ' petting zoos:</p>';
foreach ($all_zoos as $zoo) {
    $address = get_post_meta($zoo->ID, '_petting_zoo_address', true);
    echo '<li>' . $zoo->post_title . ' - ' . $address . '</li>';
}

// Test 2: Check location taxonomy
echo '<h2>Test 2: Location Terms</h2>';
$location_terms = get_terms(array(
    'taxonomy' => 'location',
    'hide_empty' => false,
    'number' => 10
));

echo '<p>Found ' . count($location_terms) . ' location terms:</p>';
foreach ($location_terms as $term) {
    $parent = get_term($term->parent);
    $parent_name = ($parent && !is_wp_error($parent)) ? ' (in ' . $parent->name . ')' : '';
    echo '<li>' . $term->name . $parent_name . '</li>';
}

// Test 3: Test search function
echo '<h2>Test 3: Search Function Test</h2>';
$test_queries = array('Texas', 'California', 'New York', 'Farm');

foreach ($test_queries as $query) {
    echo '<h3>Searching for: ' . $query . '</h3>';
    
    // Search by location taxonomy
    $location_terms = get_terms(array(
        'taxonomy' => 'location',
        'hide_empty' => false,
        'name__like' => $query
    ));
    
    echo '<p>Location terms found: ' . count($location_terms) . '</p>';
    
    $zoo_ids = array();
    
    if (!empty($location_terms)) {
        foreach ($location_terms as $term) {
            $term_zoos = get_posts(array(
                'post_type' => 'petting_zoo',
                'posts_per_page' => -1,
                'post_status' => 'publish',
                'tax_query' => array(
                    array(
                        'taxonomy' => 'location',
                        'field' => 'term_id',
                        'terms' => $term->term_id
                    )
                ),
                'fields' => 'ids'
            ));
            $zoo_ids = array_merge($zoo_ids, $term_zoos);
        }
    }
    
    // Also search by address
    $address_zoos = get_posts(array(
        'post_type' => 'petting_zoo',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => '_petting_zoo_address',
                'value' => $query,
                'compare' => 'LIKE'
            )
        ),
        'fields' => 'ids'
    ));
    
    $zoo_ids = array_merge($zoo_ids, $address_zoos);
    $zoo_ids = array_unique($zoo_ids);
    
    echo '<p>Total zoos found: ' . count($zoo_ids) . '</p>';
    
    if (!empty($zoo_ids)) {
        echo '<ul>';
        foreach (array_slice($zoo_ids, 0, 5) as $zoo_id) {
            $zoo = get_post($zoo_id);
            $address = get_post_meta($zoo_id, '_petting_zoo_address', true);
            echo '<li>' . $zoo->post_title . ' - ' . $address . '</li>';
        }
        echo '</ul>';
    }
    
    echo '<hr>';
}

// Test 4: AJAX endpoints
echo '<h2>Test 4: AJAX Endpoints</h2>';
echo '<p>AJAX URL: ' . admin_url('admin-ajax.php') . '</p>';
echo '<p>Nonce: ' . wp_create_nonce('petting_zoo_nonce') . '</p>';

// Test 5: Check if search results page exists
echo '<h2>Test 5: Search Results Page</h2>';
$search_page = get_page_by_path('search-results');
if ($search_page) {
    echo '<p>✅ Search results page exists: <a href="' . get_permalink($search_page->ID) . '">' . get_permalink($search_page->ID) . '</a></p>';
    echo '<p>Template: ' . get_post_meta($search_page->ID, '_wp_page_template', true) . '</p>';
} else {
    echo '<p>❌ Search results page does not exist</p>';
}

echo '<h2>Test Links</h2>';
echo '<p><a href="http://pettingzoo.local">Homepage</a></p>';
if ($search_page) {
    echo '<p><a href="' . get_permalink($search_page->ID) . '?search=Texas">Search Results (Texas)</a></p>';
}
?>
